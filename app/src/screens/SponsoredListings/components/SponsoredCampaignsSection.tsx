import React, { useMemo, useState } from 'react';
import { View, TouchableOpacity, SectionList } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { CampaignDtoType } from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { CategoryThumbnailWithSkeleton } from 'src/screens/Campaigns/components/CategoryThumbnailWithSkeleton';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { SponsoredCampaignCard } from './SponsoredCampaignCard';

type SponsoredCampaignsSectionProps = {
  campaigns: CampaignDtoType[];
};

type GroupedCampaigns = {
  [categoryName: string]: CampaignDtoType[];
};

type SectionData = {
  title: string;
  data: CampaignDtoType[];
  isExpanded: boolean;
};

const TEST_IDS = createTestIds('sponsored-campaigns-section', {
  CONTAINER: 'container',
  CATEGORY_ACCORDION: 'category-accordion',
});

export const SponsoredCampaignsSection: React.FC<
  SponsoredCampaignsSectionProps
> = ({ campaigns }) => {
  const isMobileScreen = useIsAtMostSmallScreenWidth();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );

  // Group campaigns by category and convert to section data
  const sectionData = useMemo(() => {
    const grouped: GroupedCampaigns = {};

    campaigns.forEach((campaign) => {
      const categoryName = campaign.category?.name || 'Other';
      if (!grouped[categoryName]) {
        grouped[categoryName] = [];
      }

      grouped[categoryName].push(campaign);
    });

    // Sort campaigns within each category: active first, then inactive
    Object.keys(grouped).forEach((categoryName) => {
      grouped[categoryName].sort((a, b) => {
        const aIsActive = a.isActive && !a.isPaused;
        const bIsActive = b.isActive && !b.isPaused;

        if (aIsActive && !bIsActive) {
          return -1;
        }

        if (!aIsActive && bIsActive) {
          return 1;
        }

        return 0;
      });
    });

    // Convert to section data format
    return Object.entries(grouped).map(([categoryName, categoryCampaigns]) => ({
      title: categoryName,
      data: expandedCategories.has(categoryName) ? categoryCampaigns : [],
      isExpanded: expandedCategories.has(categoryName),
    }));
  }, [campaigns, expandedCategories]);

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryName)) {
        newSet.delete(categoryName);
      } else {
        newSet.add(categoryName);
      }

      return newSet;
    });
  };

  const handleCampaignMenuPress = (campaign: CampaignDtoType) => {
    // TODO: Implement campaign menu actions
    // eslint-disable-next-line no-console
    console.log('Campaign menu pressed for:', campaign.campaignId);
  };

  const renderSectionHeader = ({ section }: { section: SectionData }) => {
    const categoryImage = campaigns.find(
      (c) => c.category?.name === section.title,
    )?.category;

    return (
      <TouchableOpacity
        style={styles.categoryHeader}
        onPress={() => toggleCategory(section.title)}
        testID={`${TEST_IDS.CATEGORY_ACCORDION}-${section.title}`}
      >
        <View style={styles.categoryHeaderContent}>
          <View style={styles.categoryInfo}>
            <CategoryThumbnailWithSkeleton
              categoryId={categoryImage?.categoryId || 0}
              width={40}
              height={40}
              customStyle={styles.categoryImageContainer}
            />
            <Typography useVariant="bodySemiBold" style={styles.categoryTitle}>
              {section.title}
            </Typography>
          </View>
          <Icon
            name={section.isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
          />
        </View>
      </TouchableOpacity>
    );
  };

  const renderItem = ({ item }: { item: CampaignDtoType }) => (
    <View style={styles.campaignCardWrapper}>
      <SponsoredCampaignCard
        campaign={item}
        onMenuPress={() => handleCampaignMenuPress(item)}
      />
    </View>
  );

  return (
    <View
      style={[styles.container, isMobileScreen && styles.mobileContainer]}
      testID={TEST_IDS.CONTAINER}
    >
      <SectionList
        sections={sectionData}
        keyExtractor={(item) => item.campaignId}
        renderItem={renderItem}
        renderSectionHeader={renderSectionHeader}
        stickySectionHeadersEnabled={true}
        contentContainerStyle={styles.sectionListContent}
        showsVerticalScrollIndicator={false}
        scrollEnabled={true}
      />
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    flex: 1,
    paddingHorizontal: spacing(3),
  },
  mobileContainer: {
    width: '100%',
  },
  sectionListContent: {
    gap: spacing(2),
  },
  categoryHeader: {
    paddingVertical: spacing(2),
    paddingHorizontal: spacing(2), // Reduced padding since we're using SectionList
    backgroundColor: palette.mortarV3.tokenNeutral0, // White background
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
    marginHorizontal: -spacing(3), // Extend to full width
  },
  categoryHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing(3), // Add padding back inside the header content
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryImageContainer: {
    marginRight: spacing(2),
  },
  categoryTitle: {
    color: palette.mortarV3.tokenNeutral900,
  },
  campaignCardWrapper: {
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(1),
  },
}));

SponsoredCampaignsSection.testIds = TEST_IDS;
