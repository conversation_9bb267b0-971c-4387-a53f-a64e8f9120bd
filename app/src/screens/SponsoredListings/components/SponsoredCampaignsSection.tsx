import React, { useMemo, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { CampaignDtoType } from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { CategoryThumbnailWithSkeleton } from 'src/screens/Campaigns/components/CategoryThumbnailWithSkeleton';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { SponsoredCampaignCard } from './SponsoredCampaignCard';

type SponsoredCampaignsSectionProps = {
  campaigns: CampaignDtoType[];
};

type GroupedCampaigns = {
  [categoryName: string]: CampaignDtoType[];
};

const TEST_IDS = createTestIds('sponsored-campaigns-section', {
  CONTAINER: 'container',
  CATEGORY_ACCORDION: 'category-accordion',
});

export const SponsoredCampaignsSection: React.FC<
  SponsoredCampaignsSectionProps
> = ({ campaigns }) => {
  const isMobileScreen = useIsAtMostSmallScreenWidth();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );

  // Group campaigns by category and sort within each group
  const groupedCampaigns = useMemo(() => {
    const grouped: GroupedCampaigns = {};

    campaigns.forEach((campaign) => {
      const categoryName = campaign.category?.name || 'Other';
      if (!grouped[categoryName]) {
        grouped[categoryName] = [];
      }

      grouped[categoryName].push(campaign);
    });

    // Sort campaigns within each category: active first, then inactive
    Object.keys(grouped).forEach((categoryName) => {
      grouped[categoryName].sort((a, b) => {
        const aIsActive = a.isActive && !a.isPaused;
        const bIsActive = b.isActive && !b.isPaused;

        if (aIsActive && !bIsActive) {
          return -1;
        }

        if (!aIsActive && bIsActive) {
          return 1;
        }

        return 0;
      });
    });

    return grouped;
  }, [campaigns]);

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryName)) {
        newSet.delete(categoryName);
      } else {
        newSet.add(categoryName);
      }

      return newSet;
    });
  };

  const handleCampaignMenuPress = (campaign: CampaignDtoType) => {
    // TODO: Implement campaign menu actions
    // eslint-disable-next-line no-console
    console.log('Campaign menu pressed for:', campaign.campaignId);
  };

  return (
    <View
      style={[styles.container, isMobileScreen && styles.mobileContainer]}
      testID={TEST_IDS.CONTAINER}
    >
      {Object.entries(groupedCampaigns).map(
        ([categoryName, categoryCampaigns]) => {
          const isExpanded = expandedCategories.has(categoryName);
          const categoryImage = categoryCampaigns[0]?.category;

          return (
            <View key={categoryName} style={styles.categoryContainer}>
              {/* Category Header with sticky behavior */}
              <View style={styles.categoryHeader}>
                <TouchableOpacity
                  style={styles.categoryHeaderContent}
                  onPress={() => toggleCategory(categoryName)}
                  testID={`${TEST_IDS.CATEGORY_ACCORDION}-${categoryName}`}
                >
                  <View style={styles.categoryInfo}>
                    <CategoryThumbnailWithSkeleton
                      categoryId={categoryImage?.categoryId || 0}
                      width={40}
                      height={40}
                      customStyle={styles.categoryImageContainer}
                    />
                    <Typography
                      useVariant="bodySemiBold"
                      style={styles.categoryTitle}
                    >
                      {categoryName}
                    </Typography>
                  </View>
                  <Icon
                    name={isExpanded ? 'chevron-up' : 'chevron-down'}
                    size={20}
                  />
                </TouchableOpacity>
              </View>

              {/* Campaign Cards */}
              {isExpanded && (
                <View style={styles.campaignsContainer}>
                  {categoryCampaigns.map((campaign) => (
                    <SponsoredCampaignCard
                      key={campaign.campaignId}
                      campaign={campaign}
                      onMenuPress={() => handleCampaignMenuPress(campaign)}
                    />
                  ))}
                </View>
              )}
            </View>
          );
        },
      )}
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    gap: spacing(2),
    paddingHorizontal: spacing(3),
  },
  mobileContainer: {
    width: '100%',
  },
  categoryContainer: {
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    borderRadius: spacing(1),
    overflow: 'hidden',
    backgroundColor: palette.mortarV3.tokenNeutral0,
    marginHorizontal: -spacing(3), // Extend to full width by negating container padding
  },
  categoryHeader: {
    backgroundColor: palette.mortarV3.tokenNeutral0, // White background
    borderBottomWidth: 1,
    borderBottomColor: palette.mortarV3.tokenNeutral200,
  },

  categoryHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing(2),
    paddingHorizontal: spacing(5), // Add back padding to account for negative margin
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryImageContainer: {
    marginRight: spacing(2),
  },
  categoryTitle: {
    color: palette.mortarV3.tokenNeutral900,
  },
  campaignsContainer: {
    paddingHorizontal: spacing(5), // Match header padding to account for negative margin
    paddingVertical: spacing(2),
    gap: spacing(2),
  },
}));

SponsoredCampaignsSection.testIds = TEST_IDS;
