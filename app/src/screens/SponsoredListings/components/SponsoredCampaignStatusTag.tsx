import React from 'react';
import { View } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

type SponsoredCampaignStatusTagProps = {
  isActive: boolean;
  testID?: string;
};

const TEST_IDS = createTestIds('sponsored-campaign-status-tag', {
  CONTAINER: 'container',
  ICON: 'icon',
  TEXT: 'text',
});

export const SponsoredCampaignStatusTag: React.FC<
  SponsoredCampaignStatusTagProps
> = ({ isActive, testID }) => {
  const statusConfig = isActive
    ? {
        text: 'Active',
        iconName: 'check-circle-fill' as const,
        containerStyle: styles.activeContainer,
        iconStyle: styles.activeIcon,
        textStyle: styles.activeText,
      }
    : {
        text: 'Inactive',
        iconName: 'warning-triangle-fill' as const,
        containerStyle: styles.inactiveContainer,
        iconStyle: styles.inactiveIcon,
        textStyle: styles.inactiveText,
      };

  return (
    <View
      style={[styles.container, statusConfig.containerStyle]}
      testID={testID || TEST_IDS.CONTAINER}
    >
      <Icon
        name={statusConfig.iconName}
        size={16}
        style={statusConfig.iconStyle}
        testID={TEST_IDS.ICON}
      />
      <Typography
        useVariant="labelSemiBold"
        style={statusConfig.textStyle}
        testID={TEST_IDS.TEXT}
      >
        {statusConfig.text}
      </Typography>
    </View>
  );
};

const styles = createMortarStyles(({ spacing, palette }) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing(1.5),
    paddingVertical: spacing(0.5),
    borderRadius: spacing(2),
    gap: spacing(0.5),
  },
  activeContainer: {
    backgroundColor: palette.mortarV3.tokenSuccess200, // Light green background
  },
  inactiveContainer: {
    backgroundColor: palette.mortarV3.tokenAttention50, // Light orange background
  },
  activeIcon: {
    color: palette.mortarV3.tokenSuccess500, // Green color
  },
  inactiveIcon: {
    color: palette.mortarV3.tokenAttention500, // Orange color
  },
  activeText: {
    color: palette.mortarV3.tokenSuccess500, // Green color
  },
  inactiveText: {
    color: palette.mortarV3.tokenAttention500, // Orange color
  },
}));

SponsoredCampaignStatusTag.testIds = TEST_IDS;
