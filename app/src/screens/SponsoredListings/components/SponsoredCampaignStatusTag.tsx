import React from 'react';
import { View } from 'react-native';
import { Icon, Typography } from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';

type SponsoredCampaignStatusTagProps = {
  isActive: boolean;
  testID?: string;
};

const TEST_IDS = createTestIds('sponsored-campaign-status-tag', {
  CONTAINER: 'container',
  ICON: 'icon',
  TEXT: 'text',
});

export const SponsoredCampaignStatusTag: React.FC<
  SponsoredCampaignStatusTagProps
> = ({ isActive, testID }) => {
  const statusConfig = isActive
    ? {
        text: 'Active',
        iconName: 'check-circle-fill' as const,
        containerStyle: styles.activeContainer,
        iconStyle: styles.activeIcon,
        textStyle: styles.activeText,
      }
    : {
        text: 'Inactive',
        iconName: 'warning-triangle-fill' as const,
        containerStyle: styles.inactiveContainer,
        iconStyle: styles.inactiveIcon,
        textStyle: styles.inactiveText,
      };

  return (
    <View
      style={[styles.container, statusConfig.containerStyle]}
      testID={testID || TEST_IDS.CONTAINER}
    >
      <Icon
        name={statusConfig.iconName}
        size={16}
        style={statusConfig.iconStyle}
        testID={TEST_IDS.ICON}
      />
      <Typography
        useVariant="labelSemiBold"
        style={statusConfig.textStyle}
        testID={TEST_IDS.TEXT}
      >
        {statusConfig.text}
      </Typography>
    </View>
  );
};

const styles = createMortarStyles(({ spacing }) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing(1.5),
    paddingVertical: spacing(0.5),
    borderRadius: spacing(2), // More rounded corners like in design
    gap: spacing(0.5),
  },
  activeContainer: {
    backgroundColor: '#DCFCE7', // Light green background
  },
  inactiveContainer: {
    backgroundColor: '#FEF3C7', // Light orange background
  },
  activeIcon: {
    color: '#16A34A', // Green color
  },
  inactiveIcon: {
    color: '#D97706', // Orange color
  },
  activeText: {
    color: '#16A34A', // Green color
  },
  inactiveText: {
    color: '#D97706', // Orange color
  },
}));

SponsoredCampaignStatusTag.testIds = TEST_IDS;
