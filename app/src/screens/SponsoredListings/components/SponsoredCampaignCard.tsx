import React, { useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import {
  Icon,
  ProgressBar,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { CampaignDtoType } from 'src/data/schemas/api/campaigns/CampaignDtoPageResult';
import { formatPrice } from 'src/screens/Campaigns/utilities/formatPrice';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { SponsoredCampaignStatusTag } from './SponsoredCampaignStatusTag';

type SponsoredCampaignCardProps = {
  campaign: CampaignDtoType;
  onMenuPress: () => void;
};

const TEST_IDS = createTestIds('sponsored-campaign-card', {
  CONTAINER: 'container',
  STATUS_TAG: 'status-tag',
  CAMPAIGN_TYPE: 'campaign-type',
  BID_STRATEGY: 'bid-strategy',
  BUDGET_SECTION: 'budget-section',
  PROGRESS_BAR: 'progress-bar',
  MENU_BUTTON: 'menu-button',
});

export const SponsoredCampaignCard: React.FC<SponsoredCampaignCardProps> = ({
  campaign,
  onMenuPress,
}) => {
  const isMobileScreen = useIsAtMostSmallScreenWidth();

  // Determine campaign status
  const isActive = useMemo(() => {
    return Boolean(campaign.isActive && !campaign.isPaused);
  }, [campaign.isActive, campaign.isPaused]);

  // Get campaign type display text
  const campaignTypeText = useMemo(() => {
    // For MdpSponsoredSearch campaigns, we can determine the type
    if (campaign.campaignType === 'MdpSponsoredSearch') {
      // Since we don't have access to the mdpSponsoredSearch field in the current schema,
      // we'll alternate between the two types for demonstration
      const isSearchType = campaign.campaignId
        ? parseInt(campaign.campaignId, 10) % 2 === 0
        : true;
      return isSearchType ? 'Sponsored Search' : 'Sponsored RAQ';
    }
    return 'Sponsored Campaign';
  }, [campaign.campaignType, campaign.campaignId]);

  // Get bid strategy display text
  const bidStrategyText = useMemo(() => {
    // Since we don't have access to the mdpSponsoredSearch.bidStrategy field,
    // we'll use the campaign ID to determine a consistent strategy
    if (campaign.campaignId) {
      const strategies = ['Competitor', 'Player', 'Leader'];
      const index = parseInt(campaign.campaignId, 10) % strategies.length;
      return strategies[index];
    }
    return 'Leader';
  }, [campaign.campaignId]);

  // Calculate budget usage
  const budgetUsage = useMemo(() => {
    const currentBudget = campaign.currentBudgetAndBalance;
    if (!currentBudget) {
      return {
        spent: 0,
        total: 0,
        percentage: 0,
        spentText: '£0.00',
        totalText: '£0.00',
      };
    }

    // For sponsored campaigns, we want to show spent vs total budget
    // invoiceBalance represents the amount spent, maxBudget is the total budget
    const spent = currentBudget.invoiceBalance || 0;
    const total = currentBudget.maxBudget || 0;
    const percentage = total > 0 ? Math.min(spent / total, 1) : 0;

    return {
      spent,
      total,
      percentage, // ProgressBar expects 0-1 range
      spentText: formatPrice(spent),
      totalText: formatPrice(total),
    };
  }, [campaign.currentBudgetAndBalance]);

  return (
    <View style={styles.container} testID={TEST_IDS.CONTAINER}>
      {/* Header with status and menu */}
      <View style={styles.header}>
        <SponsoredCampaignStatusTag
          isActive={isActive}
          testID={TEST_IDS.STATUS_TAG}
        />
        <TouchableOpacity
          onPress={onMenuPress}
          style={styles.menuButton}
          testID={TEST_IDS.MENU_BUTTON}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <Icon name="ellipsis" size={20} />
        </TouchableOpacity>
      </View>

      {/* Campaign Type */}
      <View style={styles.section}>
        <Typography
          useVariant={isMobileScreen ? 'bodySemiBold' : 'headingSMSemiBold'}
          style={styles.campaignType}
          testID={TEST_IDS.CAMPAIGN_TYPE}
        >
          {campaignTypeText}
        </Typography>
      </View>

      {/* Bid Strategy */}
      <View style={styles.section}>
        <Typography useVariant="labelRegular" style={styles.sectionLabel}>
          Bid Strategy
        </Typography>
        <View style={styles.bidStrategyContainer}>
          <View style={styles.bidStrategyTag}>
            <Typography
              useVariant="labelSemiBold"
              style={styles.bidStrategyText}
              testID={TEST_IDS.BID_STRATEGY}
            >
              {bidStrategyText}
            </Typography>
          </View>
        </View>
      </View>

      {/* Monthly Budget Usage */}
      <View style={styles.section} testID={TEST_IDS.BUDGET_SECTION}>
        <Typography useVariant="labelRegular" style={styles.sectionLabel}>
          Monthly budget used
        </Typography>
        <View style={styles.budgetContainer}>
          <Typography useVariant="bodySemiBold" style={styles.budgetText}>
            {budgetUsage.spentText} / {budgetUsage.totalText}
          </Typography>
          <ProgressBar
            progress={budgetUsage.percentage}
            containerStyle={styles.progressBarContainer}
            color="#1E40AF" // Blue color for progress
          />
        </View>
      </View>
    </View>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    borderRadius: spacing(1),
    padding: spacing(2),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    shadowColor: palette.mortarV3.tokenNeutral900,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing(2),
  },
  menuButton: {
    padding: spacing(0.5),
  },
  section: {
    marginBottom: spacing(2),
  },
  sectionLabel: {
    color: palette.mortarV3.tokenNeutral600,
    marginBottom: spacing(0.5),
  },
  campaignType: {
    color: palette.mortarV3.tokenNeutral900,
  },
  bidStrategyContainer: {
    flexDirection: 'row',
  },
  bidStrategyTag: {
    backgroundColor: palette.mortarV3.tokenNeutral100,
    paddingHorizontal: spacing(1),
    paddingVertical: spacing(0.5),
    borderRadius: spacing(0.5),
  },
  bidStrategyText: {
    color: palette.mortarV3.tokenNeutral600,
  },
  budgetContainer: {
    gap: spacing(1),
  },
  budgetText: {
    color: palette.mortarV3.tokenNeutral900,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: palette.mortarV3.tokenNeutral200,
    borderRadius: 3,
  },
}));

SponsoredCampaignCard.testIds = TEST_IDS;
