import React from 'react';
import { cleanup, render, fireEvent } from '@testing-library/react-native';
import { useNavigation } from '@react-navigation/native';
import { useUserContext } from 'src/hooks/useUser';
import { showToast } from 'src/components';
import { CREATE_SPONSORED_LISTING_SCREEN } from 'src/constants';
import { createQueryClientWrapper } from 'src/utilities/tanstack-query/tanstack-test-utils';
import { SponsoredListings } from './SponsoredListings';
import {
  useGetCampaignStats,
  useGetCampaignStatsForAllPeriods,
} from './hooks/useGetCampaignStats';
import { useSponsoredListingCampaigns } from './hooks/useSponsoredListingCampaigns';

jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
  useRoute: jest.fn(),
}));

jest.mock('src/hooks/useUser', () => ({
  useUserContext: jest.fn(),
}));

jest.mock('src/components', () => ({
  ...jest.requireActual('src/components'),
  showToast: jest.fn(),
}));

jest.mock('./hooks/useGetCampaignStats', () => ({
  useGetCampaignStats: jest.fn(),
  useGetCampaignStatsForAllPeriods: jest.fn(),
}));

jest.mock('./hooks/useSponsoredListingCampaigns', () => ({
  useSponsoredListingCampaigns: jest.fn(),
}));

jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: jest.requireActual('react-native').View,
  useSafeAreaInsets: () => ({ bottom: 0 }),
}));

jest.mock('src/services/datadog/datadog', () => ({
  addAction: jest.fn(),
  captureException: jest.fn(),
}));

jest.mock('src/services/analytics', () => ({
  logEvent: jest.fn(),
}));

describe('SponsoredListings', () => {
  const mockNavigate = jest.fn();
  const defaultStats = [
    {
      impressions: 1000,
      clicks: 100,
      cpc: 2.5,
      clickThroughRate: 10.0,
      spend: 250,
    },
  ];

  const mockCampaigns = [
    { campaignId: '123', campaignType: 'Ppl' },
    { campaignId: '456', campaignType: 'Fixed' },
  ];

  beforeEach(() => {
    // Mock console.error to prevent test failures due to React errors
    jest.spyOn(console, 'error').mockImplementation(() => {
      // Intentionally suppress console.error during tests
    });

    // Mock navigation with setOptions method
    (useNavigation as jest.Mock).mockReturnValue({
      navigate: mockNavigate,
      setOptions: jest.fn(),
      canGoBack: jest.fn().mockReturnValue(true),
      goBack: jest.fn(),
      getState: jest.fn().mockReturnValue({
        routes: [],
        index: 0,
      }),
    });

    (useUserContext as jest.Mock).mockReturnValue({ companyId: 123 });
    (useGetCampaignStats as jest.Mock).mockReturnValue({
      stats: defaultStats,
      isLoading: false,
      error: null,
    });
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {
        '7d': defaultStats,
        '14d': defaultStats,
        '30d': defaultStats,
        '90d': defaultStats,
        '365d': defaultStats,
      },
      isLoading: false,
      errors: {
        '7d': null,
        '14d': null,
        '30d': null,
        '90d': null,
        '365d': null,
      },
    });
    (useSponsoredListingCampaigns as jest.Mock).mockReturnValue({
      campaigns: mockCampaigns,
      sponsoredCampaigns: [],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it('renders loader while fetching campaign stats', () => {
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {},
      isLoading: true,
      errors: {},
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    expect(getByTestId('loader')).toBeDefined();
  });

  it('renders loader while fetching campaigns', () => {
    (useSponsoredListingCampaigns as jest.Mock).mockReturnValue({
      campaigns: [],
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    expect(getByTestId('loader')).toBeDefined();
  });

  it('renders create button and performance metrics when data is available', () => {
    const wrapper = createQueryClientWrapper();
    const { getByTestId, getByText } = render(<SponsoredListings />, {
      wrapper,
    });

    expect(getByTestId(SponsoredListings.testIds!.CREATE_BUTTON)).toBeDefined();
    expect(
      getByTestId(SponsoredListings.testIds!.PERFORMANCE_SECTION),
    ).toBeDefined();
    expect(getByText('Your listings performance')).toBeDefined();
  });

  it('shows empty state when no metrics are available', () => {
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {},
      isLoading: false,
      errors: {},
    });

    const wrapper = createQueryClientWrapper();
    const { getByTestId, getByText } = render(<SponsoredListings />, {
      wrapper,
    });

    expect(getByTestId(SponsoredListings.testIds!.EMPTY_STATE)).toBeDefined();
    expect(getByText('No sponsored listings to show')).toBeDefined();
  });

  it('navigates to create screen when create button is pressed', () => {
    const wrapper = createQueryClientWrapper();
    const { getByTestId } = render(<SponsoredListings />, { wrapper });

    const createButton = getByTestId(SponsoredListings.testIds!.CREATE_BUTTON);
    fireEvent.press(createButton);

    expect(mockNavigate).toHaveBeenCalledWith(CREATE_SPONSORED_LISTING_SCREEN);
  });

  it('shows error toast when campaign stats fetch fails', () => {
    const error = new Error('Failed to fetch');
    (useGetCampaignStatsForAllPeriods as jest.Mock).mockReturnValue({
      statsMap: {},
      isLoading: false,
      errors: {
        '7d': error,
        '14d': null,
        '30d': null,
        '90d': null,
        '365d': null,
      },
    });

    const wrapper = createQueryClientWrapper();
    render(<SponsoredListings />, { wrapper });

    expect(showToast).toHaveBeenCalledWith({
      text1: 'Unable to retrieve campaigns statistics right now',
      type: 'error',
      position: 'bottom',
      autoHide: true,
    });
  });

  it('updates time period when selector is changed', async () => {
    const wrapper = createQueryClientWrapper();
    const { getByText } = render(<SponsoredListings />, { wrapper });

    const thirtyDaysButton = getByText('30d');
    fireEvent.press(thirtyDaysButton);

    // Since we're now using useGetCampaignStatsForAllPeriods which is called once
    // with all time periods, we just verify it was called with the right parameters
    expect(useGetCampaignStatsForAllPeriods).toHaveBeenCalledWith(
      expect.objectContaining({
        dimensions: ['companyId'],
        companyId: 123,
        timePeriods: expect.any(Array),
      }),
    );
  });

  it('initializes with all time periods', () => {
    const wrapper = createQueryClientWrapper();
    render(<SponsoredListings />, { wrapper });

    expect(useGetCampaignStatsForAllPeriods).toHaveBeenCalledWith(
      expect.objectContaining({
        dimensions: ['companyId'],
        companyId: 123,
        timePeriods: expect.any(Array),
      }),
    );
  });
});
