import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { View, TouchableOpacity } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import {
  BasicAccordion,
  Button,
  Icon,
  Loader,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { useNavigation } from '@react-navigation/native';
import { ContentSegment } from 'src/components/ContentSegment';
import { WebStyledScrollView } from 'src/components/WebStyledScrollView/WebStyledScrollView';
import { CREATE_SPONSORED_LISTING_SCREEN, HOME_SCREEN } from 'src/constants';
import { useUserContext } from 'src/hooks/useUser';
import { useIsAtMostSmallScreenWidth } from 'src/hooks/useMediaQuery';
import { NavBackIcon, showToast } from 'src/components';
import { logEvent } from 'src/services/analytics';
import { EVENT_TYPE } from 'src/constants.events';
import { captureException } from 'src/services/datadog';
import { MetricCardsSponsoredListings } from './components/PerformanceMetrics/MetricCardsSponsoredListings';
import { useGetCampaignStatsForAllPeriods } from './hooks/useGetCampaignStats';
import { getDateRangeFromTimePeriod } from './utils/getDateRangeFromTimePeriod';
import {
  StatisticsSchemaType,
  TimePeriodsEnum,
  TimePeriodsType,
} from './Types/dashboardSponsoredListingsSchema';
import { DateRangeIndicatorSponsoredListings } from './components/DateRangeIndicatorSponsoredListings';
import { TimePeriodSelector } from '../ActiveHome/components/TimePeriodSelector';
import { SPONSORED_LISTING } from './constants';
import { useSponsoredListingCampaigns } from './hooks/useSponsoredListingCampaigns';
import { SponsoredCampaignsSection } from './components/SponsoredCampaignsSection';

type TestIds = {
  ROOT: string;
  CONTENT: string;
  CREATE_BUTTON: string;
  EMPTY_STATE: string;
  PERFORMANCE_SECTION: string;
  TITLE: string;
  WHAT_ARE_SPONSORED_LISTINGS: string;
  HOW_SPONSORED_LISTINGS_WORK: string;
  LISTINGS_CARDS_SECTION: string;
};

const TEST_IDS: TestIds = createTestIds('sponsored-listings', {
  ROOT: 'root',
  CONTENT: 'content',
  CREATE_BUTTON: 'create-button',
  EMPTY_STATE: 'empty-state',
  PERFORMANCE_SECTION: 'performance-section',
  TITLE: 'title',
  WHAT_ARE_SPONSORED_LISTINGS: 'what-are-sponsored-listings',
  HOW_SPONSORED_LISTINGS_WORK: 'how-sponsored-listings-work',
  LISTINGS_CARDS_SECTION: 'listings-cards-section',
});

export const SponsoredListings: React.FC = () => {
  const navigation = useNavigation();
  const {
    sponsoredCampaigns,
    campaigns,
    isLoading: isCampaignsLoading,
    error: campaignsError,
  } = useSponsoredListingCampaigns();
  const { bottom } = useSafeAreaInsets();
  const { companyId } = useUserContext();
  const [selectedTimePeriod, setSelectedTimePeriod] =
    useState<TimePeriodsType>();
  const [isExpandedUI, setIsExpandedUI] = useState(false);
  const [isPerformanceExpanded, setIsPerformanceExpanded] = useState(false);
  const isMobileScreen = useIsAtMostSmallScreenWidth();

  const initialTimePeriod = TimePeriodsEnum.SevenDays;
  const performanceTimePeriods: TimePeriodsType[] = [
    TimePeriodsEnum.SevenDays,
    TimePeriodsEnum.FourteenDays,
    TimePeriodsEnum.ThirtyDays,
    TimePeriodsEnum.NinetyDays,
    TimePeriodsEnum.ThreeHundredSixtyFiveDays,
  ];

  const dateRange = useMemo(() => {
    const period = selectedTimePeriod || initialTimePeriod;
    return getDateRangeFromTimePeriod(period);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTimePeriod]);

  // Fetch data for all time periods at once
  const { statsMap, isLoading, errors } = useGetCampaignStatsForAllPeriods({
    timePeriods: performanceTimePeriods,
    dimensions: ['companyId'],
    companyId: companyId ?? 0,
  });

  useEffect(() => {
    if (!isLoading && !isCampaignsLoading) {
      logEvent(EVENT_TYPE.SPONSORED_LISTINGS_VIEWED);
    }
  }, [isCampaignsLoading, isLoading]);

  useEffect(() => {
    if (campaignsError) {
      captureException(campaignsError, {
        tags: {
          module: 'SponsoredListings',
          method: 'useSponsoredListingCampaigns',
        },
      });
    }
  }, [campaignsError]);

  useEffect(() => {
    // Check if there are any errors for any time period
    const hasErrors = Object.values(errors).some((error) => Boolean(error));
    if (hasErrors) {
      showToast({
        text1: SPONSORED_LISTING.TOAST_ERROR,
        type: 'error',
        position: 'bottom',
        autoHide: true,
      });
      console.error('Failed to fetch campaign stats:', errors);
      captureException(errors, {
        tags: {
          module: 'SponsoredListings',
          method: 'useGetCampaignStatsForAllPeriods',
        },
      });
    }
  }, [errors]);

  const handleBackPress = useCallback(() => {
    if (navigation.canGoBack()) {
      const state = navigation.getState();

      if (state && state.routes && state.index > 0) {
        const previousRoute = state.routes[state.index - 1];

        if (
          previousRoute &&
          previousRoute.name === CREATE_SPONSORED_LISTING_SCREEN
        ) {
          navigation.navigate(HOME_SCREEN);
          return;
        }
      }

      navigation.goBack();
    } else {
      navigation.navigate(HOME_SCREEN);
    }
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => <NavBackIcon onPress={handleBackPress} />,
    });
  }, [navigation, handleBackPress]);

  const performanceData = useMemo(() => {
    // Create a map of all time periods to their stats
    const result: Partial<Record<TimePeriodsType, StatisticsSchemaType>> = {};

    // Process each time period's stats
    Object.entries(statsMap).forEach(([timePeriod, stats]) => {
      if (stats && stats.length > 0) {
        const statsData = stats[0] || {
          impressions: 0,
          clicks: 0,
          cpc: 0,
          clickThroughRate: 0,
          spend: 0,
        };

        result[timePeriod as TimePeriodsType] = {
          impressions: statsData.impressions,
          clicks: statsData.clicks,
          cpc: statsData.cpc,
          clickThroughRate: statsData.clickThroughRate,
          spend: statsData.spend,
        };
      }
    });

    return Object.keys(result).length > 0 ? result : null;
  }, [statsMap]);

  if (isLoading) {
    return <Loader />;
  }

  const hasMetrics = performanceData && Object.keys(performanceData).length > 0;

  const handleCreateListing = () => {
    navigation.navigate(CREATE_SPONSORED_LISTING_SCREEN);
    logEvent(EVENT_TYPE.SPONSORED_LISTINGS_CREATE_BUTTON_CLICKED);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.content} testID={TEST_IDS.ROOT}>
        <WebStyledScrollView
          contentContainerStyle={[
            styles.scrollContainer,
            styles.whiteScrollBackground,
          ]}
          contentInset={{ bottom }}
          testID={TEST_IDS.CONTENT}
        >
          <ContentSegment style={styles.segment}>
            <View style={styles.headerContainer}>
              <Typography
                useVariant={
                  isMobileScreen ? 'bodySemiBold' : 'headingSMSemiBold'
                }
                style={isMobileScreen ? styles.ctaTextMobile : styles.ctaText}
              >
                {SPONSORED_LISTING.CREATE_SPONSORED_LISTING_HEADER}
              </Typography>
            </View>

            {isCampaignsLoading ? (
              <Loader />
            ) : campaigns?.length > 0 ? (
              <Button
                testID={TEST_IDS.CREATE_BUTTON}
                label={SPONSORED_LISTING.CREATE_SPONSORED_LISTING}
                variant="secondary"
                block
                textStyle={isMobileScreen && styles.createButtonTextMobile}
                onPress={handleCreateListing}
                style={
                  isMobileScreen
                    ? styles.createButtonMobile
                    : styles.createButton
                }
              />
            ) : (
              <View
                style={styles.emptyStateContainer}
                testID={TEST_IDS.EMPTY_STATE}
              >
                <Typography use="bodyRegular" style={styles.emptyStateText}>
                  {SPONSORED_LISTING.EMPTY_STATE}
                </Typography>
              </View>
            )}

            <BasicAccordion
              primaryItem={SPONSORED_LISTING.WHAT_ARE_SPONSORED_LISTINGS_TITLE}
              primaryItemVariant="bodySemiBold"
              testID={TEST_IDS.WHAT_ARE_SPONSORED_LISTINGS}
              style={styles.moreInfoAccordion}
              headerStyle={styles.accordionHeader}
            >
              <View style={styles.accordionContent}>
                <Typography
                  useVariant="bodyRegular"
                  style={styles.moreInfoText}
                >
                  {SPONSORED_LISTING.CREATE_SPONSORED_LISTING_DESCRIPTION}
                </Typography>
              </View>
            </BasicAccordion>
            <BasicAccordion
              primaryItem={SPONSORED_LISTING.HOW_SPONSORED_LISTINGS_WORK_TITLE}
              primaryItemVariant="bodySemiBold"
              testID={TEST_IDS.HOW_SPONSORED_LISTINGS_WORK}
              style={styles.moreInfoAccordion}
              headerStyle={styles.accordionHeader}
              onToggle={() => setIsExpandedUI(!isExpandedUI)}
            >
              <View style={styles.accordionContent}>
                <Typography
                  useVariant="bodyRegular"
                  style={styles.moreInfoText}
                >
                  {SPONSORED_LISTING.HOW_SPONSORED_LISTINGS_WORK_CONTENT}
                </Typography>
              </View>
            </BasicAccordion>
          </ContentSegment>
          <ContentSegment style={[styles.segment, styles.campaignsSegment]}>
            {sponsoredCampaigns && sponsoredCampaigns.length > 0 ? (
              <SponsoredCampaignsSection campaigns={sponsoredCampaigns} />
            ) : (
              <View
                style={styles.emptyStateContainer}
                testID={TEST_IDS.LISTINGS_CARDS_SECTION}
              >
                <Typography useVariant="bodyBold" style={styles.emptyStateText}>
                  {SPONSORED_LISTING.SPONSORED_LISTING_EMPTY_STATE}
                </Typography>
              </View>
            )}
          </ContentSegment>
          <ContentSegment style={styles.segment}>
            {hasMetrics ? (
              <View
                style={styles.performanceAccordionContainer}
                testID={TEST_IDS.PERFORMANCE_SECTION}
              >
                <TouchableOpacity
                  style={styles.performanceAccordionHeader}
                  onPress={() =>
                    setIsPerformanceExpanded(!isPerformanceExpanded)
                  }
                >
                  <View style={styles.performanceAccordionTitleContainer}>
                    <View style={styles.iconCircle}>
                      <Icon name="bar-chart" size={20} />
                    </View>
                    <Typography
                      useVariant="bodySemiBold"
                      style={
                        isMobileScreen
                          ? styles.performanceAccordionTitleMobile
                          : styles.performanceAccordionTitle
                      }
                    >
                      {SPONSORED_LISTING.PERFORMANCE_HEADER}
                    </Typography>
                  </View>
                  <View style={styles.performanceAccordionActions}>
                    <Icon
                      name={
                        isPerformanceExpanded ? 'chevron-up' : 'chevron-down'
                      }
                      size={20}
                    />
                  </View>
                </TouchableOpacity>

                {isPerformanceExpanded && (
                  <View style={styles.performanceAccordionContent}>
                    <View style={styles.divider} />

                    <TimePeriodSelector
                      timePeriods={performanceTimePeriods}
                      noTimePeriodData={false}
                      selectedTimePeriod={
                        selectedTimePeriod || initialTimePeriod
                      }
                      setSelectedTimePeriod={setSelectedTimePeriod}
                    />

                    <DateRangeIndicatorSponsoredListings
                      from={dateRange.from}
                      to={dateRange.to}
                    />

                    <MetricCardsSponsoredListings
                      metrics={
                        performanceData &&
                        performanceData[selectedTimePeriod || initialTimePeriod]
                      }
                      timePeriod={selectedTimePeriod || initialTimePeriod}
                    />
                  </View>
                )}
              </View>
            ) : (
              <View
                style={styles.performanceAccordionContainer}
                testID={TEST_IDS.PERFORMANCE_SECTION}
              >
                <TouchableOpacity
                  style={styles.performanceAccordionHeader}
                  onPress={() =>
                    setIsPerformanceExpanded(!isPerformanceExpanded)
                  }
                >
                  <View style={styles.performanceAccordionTitleContainer}>
                    <View style={styles.iconCircle}>
                      <Icon name="show-chart" size={20} />
                    </View>
                    <Typography
                      useVariant="bodySemiBold"
                      style={
                        isMobileScreen
                          ? styles.performanceAccordionTitleMobile
                          : styles.performanceAccordionTitle
                      }
                    >
                      {SPONSORED_LISTING.PERFORMANCE_HEADER}
                    </Typography>
                  </View>
                  <View style={styles.performanceAccordionActions}>
                    <Icon
                      name={
                        isPerformanceExpanded ? 'chevron-up' : 'chevron-down'
                      }
                      size={20}
                    />
                  </View>
                </TouchableOpacity>

                {isPerformanceExpanded && (
                  <View style={styles.performanceAccordionContent}>
                    <View style={styles.divider} />
                    <View
                      style={styles.emptyStateContainer}
                      testID={TEST_IDS.EMPTY_STATE}
                    >
                      <Typography
                        useVariant="bodyBold"
                        style={styles.emptyStateText}
                      >
                        {'No performance data available'}
                      </Typography>
                    </View>
                  </View>
                )}
              </View>
            )}
          </ContentSegment>
        </WebStyledScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = createMortarStyles(({ palette, spacing }) => ({
  safeArea: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  header: {
    paddingHorizontal: spacing(3),
    paddingVertical: spacing(2),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    flexDirection: 'column',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
  },
  content: {
    flex: 1,
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  scrollContainer: {
    gap: spacing(1),
    paddingBottom: spacing(10),
  },
  segment: {
    gap: spacing(3),
  },
  campaignsSegment: {
    flex: 1,
    paddingHorizontal: 0, // Remove horizontal padding for full width cards
    paddingVertical: 0, // Remove vertical padding to allow SectionList to manage spacing
  },
  headerContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: spacing(2),
  },
  ctaText: {
    textAlign: 'center',
    maxWidth: '80%',
  },
  ctaTextMobile: {
    fontSize: 18,
    lineHeight: 24,
    maxWidth: '95%',
    textAlign: 'center',
  },
  createButton: {
    marginBottom: spacing(3),
  },
  createButtonMobile: {
    marginHorizontal: spacing(2),
    paddingVertical: spacing(2),
    borderRadius: spacing(1),
  },
  createButtonTextMobile: {
    fontSize: 13,
  },
  descriptionText: {
    marginBottom: spacing(3),
    color: palette.mortarV3.tokenNeutral600,
    lineHeight: 22,
  },
  descriptionTextMobile: {
    paddingHorizontal: spacing(2),
    fontSize: 14,
  },
  performanceContent: {
    gap: spacing(2),
  },
  sectionTitle: {
    marginBottom: spacing(1),
    textAlign: 'center',
  },
  sectionTitleMobile: {
    marginBottom: spacing(1),
  },
  emptyStateContainer: {
    borderWidth: 2,
    borderStyle: 'dotted',
    borderRadius: spacing(0.5),
    borderColor: palette.mortarV3.tokenNeutral500,
    paddingVertical: spacing(3),
    paddingHorizontal: spacing(2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    color: palette.mortarV3.tokenNeutral600,
  },
  moreInfoAccordion: {
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    borderRadius: spacing(1),
    backgroundColor: palette.mortarV3.tokenNeutral0,
    overflow: 'hidden',
    marginTop: spacing(1),
  },
  accordionHeader: {
    paddingHorizontal: spacing(2),
    paddingVertical: spacing(2),
  },
  accordionContent: {
    padding: spacing(3),
    paddingTop: spacing(2),
  },
  moreInfoText: {
    color: palette.mortarV3.tokenNeutral600,
    lineHeight: 22,
  },
  paragraphSpacing: {
    marginBottom: spacing(2),
  },
  whiteScrollBackground: {
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  performanceAccordionContainer: {
    marginBottom: spacing(2),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral300,
    borderRadius: spacing(1),
    overflow: 'hidden',
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  performanceAccordionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing(2),
    paddingVertical: spacing(3),
    backgroundColor: palette.mortar.tokenColorPrimaryWhite,
  },
  performanceAccordionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  performanceAccordionTitle: {
    fontSize: 18,
    color: palette.mortarV3.tokenNeutral900,
  },
  performanceAccordionTitleMobile: {
    fontSize: 14,
  },
  performanceAccordionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  performanceAccordionContent: {
    padding: spacing(2),
    gap: spacing(2),
  },
  iconCircle: {
    width: spacing(5),
    height: spacing(5),
    borderRadius: spacing(2.5),
    backgroundColor: palette.mortarV3.tokenNeutral100,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing(2),
    display: 'flex',
  },
  divider: {
    height: 1,
    backgroundColor: palette.mortarV3.tokenNeutral200,
    marginBottom: spacing(2),
  },
}));

SponsoredListings.testIds = TEST_IDS;
